# Goszakup Parser - Chrome Extension

Chrome расширение для парсинга данных с двух порталов госзакупок Казахстана:
- **goszakup.gov.kz** (основной портал госзакупок)
- **zakup.sk.kz** (Самрук Казына)

## Возможности

<<<<<<< HEAD
- 🔍 Автоматический парсинг данных о закупках с двух сайтов
- 📊 Автоматическая отправка данных на WebHook в JSON формате
=======
- 🔍 Автоматический парсинг данных о закупках
- 📊 Отправка данных на WebHook в JSON формате
>>>>>>> 9915ab9164b7c50bbcbfc8eff0c0813f82c671c8
- 💾 Сохранение спарсенных данных
- 🎯 Удобный интерфейс управления
- 📈 Поддержка нескольких лотов для Самрук Казына

## Установка

1. Откройте Chrome и перейдите в `chrome://extensions/`
2. Включите "Режим разработчика" в правом верхнем углу
3. Нажмите "Загрузить распакованное расширение"
4. Выберите папку с этим проектом
5. Расширение установлено!

## Использование

### Парсинг данных

**Для goszakup.gov.kz:**
1. Перейдите на сайт https://v3bl.goszakup.gov.kz/
2. Найдите нужную закупку
3. Нажмите на кнопку расширения в панели инструментов Chrome
4. В открывшемся окне нажмите "Парсить данные"
<<<<<<< HEAD
5. Данные автоматически спарсятся и отправятся на webhook

**Для zakup.sk.kz (Самрук Казына):**
1. Перейдите на сайт https://zakup.sk.kz/
2. Откройте нужное объявление о закупке
3. Нажмите на кнопку расширения в панели инструментов Chrome
4. В открывшемся окне нажмите "Парсить данные"
5. Данные по всем лотам спарсятся и отправятся на webhook
=======
5. Кликните на элемент с номером лота (например: `123456-ЗЦП1`)
6. Данные автоматически спарсятся и отобразятся в интерфейсе
>>>>>>> 9915ab9164b7c50bbcbfc8eff0c0813f82c671c8

### Альтернативные способы парсинга

- **Контекстное меню**: Правый клик на странице → "Парсить данные Goszakup" (только для goszakup.gov.kz)

### Автоматическая отправка данных

<<<<<<< HEAD
После парсинга данные автоматически отправляются на WebHook в JSON-формате для интеграции с Битрикс24. 
=======
1. После парсинга данные автоматически попадают на WebHook 
>>>>>>> 9915ab9164b7c50bbcbfc8eff0c0813f82c671c8

## Парсируемые данные

### Для goszakup.gov.kz:
1. **Номер лота** - идентификатор лота (пример: 78712037-ЗЦП1)
2. **Заказчик** - наименование заказчика
3. **Дата начала приёма заявок** - формат: 2025-05-30 09:30:00
4. **Дата окончания приёма заявок** - формат: 2025-05-30 09:30:00
5. **Наименование товара** - из поля "Дополнительная характеристика"
6. **Количество товара** - количество единиц товара
7. **Объявленная сумма** - запланированная сумма закупки
8. **Источник** - ссылка на страницу лота на goszakup.gov.kz

### Для zakup.sk.kz (Самрук Казына):
1. **Номер объявления** - идентификатор объявления (пример: 1115529)
2. **Заказчик** - наименование заказчика (пример: АО "НК "Қазақстан темір жолы")
3. **Метод закупки** - способ проведения закупки (пример: Запрос ценовых предложений на понижение)
4. **Дата начала торгов** - формат: 2025-05-27 15:52:00
5. **Дата окончания торгов** - формат: 2025-05-27 15:52:00
6. **Источник** - ссылка на объявление на zakup.sk.kz
7. **Лоты** (если несколько):
   - Номер лота и наименование
   - Количество товара
   - Сумма лота

## Структура проекта

```
Zakup Parser/
├── manifest.json          # Конфигурация расширения
├── content.js             # Основная логика парсинга
├── popup.html            # HTML интерфейса
├── popup.js              # JavaScript интерфейса
├── popup.css             # Стили интерфейса
├── content.css           # Стили для веб-страницы
├── background.js         # Фоновый скрипт
├── generate_icons.html   # Генератор иконок
└── README.md            # Этот файл
```

## Технические детails

- **Manifest Version**: 3 (последняя версия)
- **Permissions**: 
  - `activeTab` - доступ к активной вкладке
  - `storage` - сохранение данных
<<<<<<< HEAD
- **Host Permissions**: 
  - `https://v3bl.goszakup.gov.kz/*` - основной портал госзакупок
  - `https://zakup.sk.kz/*` - портал Самрук Казына

## WebHook Integration

Все спарсенные данные автоматически отправляются на webhook:
`https://beget.prodvig.kz/webhook/dd8372ba-2b7a-49e7-b677-2c484cb5cf0c`

Формат отправляемых данных:
```json
{
  "timestamp": "2025-06-29T10:30:00.000Z",
  "source": "Goszakup Parser Extension",
  "data": {
    "source": "goszakup|samruk",
    "sourceUrl": "https://...",
    // ... остальные поля в зависимости от сайта
  }
}
```
=======
- **Host Permissions**: `https://v3bl.goszakup.gov.kz/*`
>>>>>>> 9915ab9164b7c50bbcbfc8eff0c0813f82c671c8



## Troubleshooting

### Расширение не работает
- Убедитесь, что вы на поддерживаемом сайте (goszakup.gov.kz или zakup.sk.kz)
- Проверьте, что расширение включено в `chrome://extensions/`
- Обновите страницу и попробуйте снова

### Данные не парсятся
- Убедитесь, что элементы с данными загружены на странице
- Для goszakup.gov.kz: попробуйте сначала кликнуть на номер лота, затем парсить
- Для zakup.sk.kz: убедитесь, что объявление полностью загружено
- Проверьте консоль разработчика (F12) на наличие ошибок

<<<<<<< HEAD
### Данные не отправляются на webhook
- Проверьте подключение к интернету
- Убедитесь, что webhook URL доступен
- Проверьте консоль разработчика для сообщений об ошибках

=======
>>>>>>> 9915ab9164b7c50bbcbfc8eff0c0813f82c671c8


## Обновления

Версия 1.0 (2025-06-29)
<<<<<<< HEAD
- Поддержка двух сайтов: goszakup.gov.kz и zakup.sk.kz
- Автоматическая отправка данных на WebHook для интеграции с Битрикс24
- Парсинг множественных лотов для Самрук Казына
- Удобный интерфейс с отображением данных по типу сайта
- Контекстное меню для быстрого парсинга
=======
- Первоначальный релиз
- Базовый функционал парсинга
- Отправка данных на WebHook
- Интерфейс управления
>>>>>>> 9915ab9164b7c50bbcbfc8eff0c0813f82c671c8

## Лицензия

MIT License - свободное использование и модификация

## Поддержка

При возникновении проблем или предложений по улучшению, создайте issue в репозитории проекта.
