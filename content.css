/* Стили для кнопки парсинга на странице */
#goszakup-parse-btn {
    transition: all 0.3s ease !important;
}

#goszakup-parse-btn:hover {
    background: #218838 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3) !important;
}

/* Подсветка активных элементов */
.btn-select-lot {
    transition: all 0.2s ease !important;
}

.btn-select-lot:hover {
    background-color: #ffc107 !important;
    border-color: #ffc107 !important;
    transform: scale(1.02) !important;
}

/* Стили для таблицы с данными лота */
.table.table-bordered.table-hover {
    box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
    border-radius: 8px !important;
    overflow: hidden !important;
}

.table.table-bordered.table-hover th {
    background-color: #f8f9fa !important;
    font-weight: 600 !important;
    color: #495057 !important;
}

.table.table-bordered.table-hover td {
    background-color: #ffffff !important;
}

.table.table-bordered.table-hover tr:hover td {
    background-color: #f1f3f4 !important;
}

/* Индикатор парсинга */
.parsing-indicator {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    background: rgba(0,0,0,0.8) !important;
    color: white !important;
    padding: 20px 30px !important;
    border-radius: 10px !important;
    z-index: 10001 !important;
    font-size: 16px !important;
    display: none !important;
}

.parsing-indicator.show {
    display: block !important;
}

/* Spinner для загрузки */
.spinner {
    border: 3px solid #f3f3f3 !important;
    border-top: 3px solid #3498db !important;
    border-radius: 50% !important;
    width: 20px !important;
    height: 20px !important;
    animation: spin 1s linear infinite !important;
    display: inline-block !important;
    margin-right: 10px !important;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
