document.addEventListener('DOMContentLoaded', function() {
    const parseBtn = document.getElementById('parseBtn');
    const clearBtn = document.getElementById('clearBtn');
    const status = document.getElementById('status');

    // Загружаем сохраненные данные при открытии popup
    loadSavedData();

    // Обработчики событий
    parseBtn.addEventListener('click', parseData);
    clearBtn.addEventListener('click', clearData);

    // Функция парсинга данных
    function parseData() {
        updateStatus('Парсинг данных...', 'info');
        
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            const currentUrl = tabs[0].url;
            let siteName = 'поддерживаемом сайте';
            
            if (currentUrl.includes('v3bl.goszakup.gov.kz')) {
                siteName = 'Goszakup';
            } else if (currentUrl.includes('zakup.sk.kz')) {
                siteName = 'Самрук Казына';
            }
            
            chrome.tabs.sendMessage(tabs[0].id, {action: 'parseData'}, function(response) {
                if (chrome.runtime.lastError) {
                    updateStatus(`Ошибка: убедитесь, что вы находитесь на сайте ${siteName}`, 'error');
                    return;
                }
                
                if (response && response.success) {
                    updateStatus(`Данные ${siteName} успешно спарсены и отправлены!`, 'success');
                    // Загружаем обновленные данные
                    setTimeout(loadSavedData, 500);
                } else {
                    updateStatus('Ошибка при парсинге данных', 'error');
                }
            });
        });
    }

    // Функция очистки данных
    function clearData() {
        chrome.storage.local.clear(function() {
            updateStatus('Данные очищены', 'info');
            clearDisplayedData();
        });
    }

    // Загрузка сохраненных данных
    function loadSavedData() {
        chrome.storage.local.get(['parsedData'], function(result) {
            if (result.parsedData) {
                displayData(result.parsedData);
                updateStatus('Данные загружены', 'success');
            } else {
                updateStatus('Нет сохраненных данных', 'info');
            }
        });
    }

    // Отображение данных в интерфейсе (для обоих сайтов)
    function displayData(data) {
        console.log('Отображаем данные в popup:', data);
        
        // Скрываем все поля сначала
        document.getElementById('goszakup-fields').style.display = 'none';
        document.getElementById('samruk-fields').style.display = 'none';
        document.getElementById('lots-section').style.display = 'none';
        
        // Отображаем поля в зависимости от источника
        if (data.source === 'goszakup') {
            document.getElementById('goszakup-fields').style.display = 'block';
            displayGoszakupData(data);
        } else if (data.source === 'samruk') {
            document.getElementById('samruk-fields').style.display = 'block';
            displaySamrukData(data);
        }
        
        // Общие поля
        const commonFields = ['source', 'customerName', 'sourceUrl'];
        commonFields.forEach(field => {
            const element = document.getElementById(field);
            if (element) {
                let value = data[field] || '-';
                if (field === 'source') {
                    value = data.source === 'goszakup' ? 'Goszakup' : 'Самрук Казына';
                }
                element.textContent = value;
                element.title = value;
            }
        });
        
        // Отображение выбранного лота
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            chrome.tabs.sendMessage(tabs[0].id, {action: 'getSelectedLotId'}, function(response) {
                const selectedLotElement = document.getElementById('selectedLotId');
                if (selectedLotElement) {
                    const selectedLotId = response && response.selectedLotId ? response.selectedLotId : '-';
                    selectedLotElement.textContent = selectedLotId;
                    selectedLotElement.title = selectedLotId;
                }
            });
        });
        
        // Отображение дат (как массив для Самрук Казына или отдельно для Goszakup)
        const startDateElement = document.getElementById('startDate');
        const endDateElement = document.getElementById('endDate');
        
        if (data.source === 'samruk' && data.tradingDates && data.tradingDates.length > 0) {
            // Для Самрук Казына показываем даты отдельно
            if (data.tradingDates.length >= 1) {
                startDateElement.textContent = data.tradingDates[0];
                startDateElement.title = 'Дата начала торгов: ' + data.tradingDates[0];
            } else {
                startDateElement.textContent = '-';
                startDateElement.title = '-';
            }
            
            if (data.tradingDates.length >= 2) {
                endDateElement.textContent = data.tradingDates[1];
                endDateElement.title = 'Дата окончания торгов: ' + data.tradingDates[1];
            } else {
                endDateElement.textContent = '-';
                endDateElement.title = '-';
            }
        } else {
            // Для Goszakup или если нет массива дат
            startDateElement.textContent = data.startDate || '-';
            startDateElement.title = data.startDate || '-';
            endDateElement.textContent = data.endDate || '-';
            endDateElement.title = data.endDate || '-';
        }
    }

    // Отображение данных для goszakup.gov.kz
    function displayGoszakupData(data) {
        const fields = ['lotNumber', 'productName', 'quantity', 'announcedSum'];
        fields.forEach(field => {
            const element = document.getElementById(field);
            if (element) {
                const value = data[field] || '-';
                element.textContent = value;
                element.title = value;
            }
        });
    }

    // Отображение данных для zakup.sk.kz
    function displaySamrukData(data) {
        const fields = ['announcementNumber', 'purchaseMethod'];
        fields.forEach(field => {
            const element = document.getElementById(field);
            if (element) {
                const value = data[field] || '-';
                element.textContent = value;
                element.title = value;
            }
        });
        
        // Отображение лотов
        if (data.lots && data.lots.length > 0) {
            document.getElementById('lots-section').style.display = 'block';
            displayLots(data.lots);
        }
    }

    // Отображение лотов
    function displayLots(lots) {
        const container = document.getElementById('lots-container');
        container.innerHTML = '';
        
        lots.forEach((lot, index) => {
            const lotDiv = document.createElement('div');
            lotDiv.className = 'lot-item';
            lotDiv.style.cssText = `
                border: 1px solid #e0e0e0;
                border-radius: 6px;
                padding: 10px;
                margin-bottom: 10px;
                background: #f9f9f9;
            `;
            
            lotDiv.innerHTML = `
                <div class="lot-header" style="font-weight: 600; margin-bottom: 5px;">
                    Лот ${index + 1}: ${lot.lotNumber || 'Не указан'}
                </div>
                <div style="font-size: 12px; line-height: 1.4;">
                    <div><strong>Наименование:</strong> ${lot.productName || '-'}</div>
                    <div><strong>Количество:</strong> ${lot.quantity || '-'}</div>
                    <div><strong>Сумма:</strong> ${lot.lotSum || '-'}</div>
                </div>
            `;
            
            container.appendChild(lotDiv);
        });
    }

    // Очистка отображаемых данных
    function clearDisplayedData() {
        const allFields = [
            'source', 'lotNumber', 'announcementNumber', 'customerName', 'purchaseMethod',
            'startDate', 'endDate', 'productName', 'sourceUrl', 'quantity', 'announcedSum'
        ];

        allFields.forEach(field => {
            const element = document.getElementById(field);
            if (element) {
                element.textContent = '-';
                element.title = '';
            }
        });
        
        // Очистка лотов
        const lotsContainer = document.getElementById('lots-container');
        if (lotsContainer) {
            lotsContainer.innerHTML = '';
        }
        
        // Скрытие секций
        document.getElementById('goszakup-fields').style.display = 'none';
        document.getElementById('samruk-fields').style.display = 'none';
        document.getElementById('lots-section').style.display = 'none';
    }

    // Обновление статуса
    function updateStatus(message, type = 'info') {
        status.textContent = message;
        status.className = `status ${type}`;
        
        // Автоматически очищаем статус через 3 секунды
        setTimeout(() => {
            status.textContent = 'Готов к работе';
            status.className = 'status';
        }, 3000);
    }
});

// Слушаем сообщения от content script
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.action === 'updateData') {
        // Обновляем отображение данных
        displayData(request.data);
    }
});
