body {
    width: 400px;
    min-height: 500px;
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
}

.container {
    padding: 15px;
    background: white;
    margin: 10px;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.header {
    text-align: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
}

.header h1 {
    margin: 0 0 5px 0;
    font-size: 20px;
    color: #2c3e50;
    font-weight: 600;
}

.header p {
    margin: 0;
    font-size: 12px;
    color: #7f8c8d;
}

.controls {
    display: flex;
    gap: 8px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.btn {
    flex: 1;
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    transition: all 0.3s ease;
    min-width: 100px;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
}

.btn-success {
    background: #27ae60;
    color: white;
}

.btn-success:hover {
    background: #229954;
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background: #7f8c8d;
}

.icon {
    font-size: 14px;
}

.status {
    padding: 8px 12px;
    border-radius: 6px;
    text-align: center;
    font-size: 12px;
    margin-bottom: 15px;
    background: #ecf0f1;
    color: #2c3e50;
    transition: all 0.3s ease;
}

.status.success {
    background: #d5f4e6;
    color: #27ae60;
    border: 1px solid #27ae60;
}

.status.error {
    background: #fdf2f2;
    color: #e74c3c;
    border: 1px solid #e74c3c;
}

.status.info {
    background: #ebf3fd;
    color: #3498db;
    border: 1px solid #3498db;
}

.data-section {
    max-height: 350px;
    overflow-y: auto;
}

.data-section h3 {
    font-size: 14px;
    margin: 15px 0 10px 0;
    color: #2c3e50;
    border-bottom: 1px solid #ecf0f1;
    padding-bottom: 5px;
}

.data-section h3:first-child {
    margin-top: 0;
}

.data-group {
    margin-bottom: 15px;
}

.data-item {
    display: flex;
    margin-bottom: 8px;
    align-items: flex-start;
}

.data-item label {
    min-width: 140px;
    font-size: 11px;
    font-weight: 600;
    color: #7f8c8d;
    margin-right: 10px;
    flex-shrink: 0;
}

.data-item span {
    font-size: 11px;
    color: #2c3e50;
    word-break: break-word;
    line-height: 1.4;
    flex: 1;
}

.data-item span:empty::after {
    content: "-";
    color: #bdc3c7;
}

.footer {
    text-align: center;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #ecf0f1;
}

.footer p {
    margin: 0;
    font-size: 10px;
    color: #95a5a6;
}

/* Scrollbar styles */
.data-section::-webkit-scrollbar {
    width: 6px;
}

.data-section::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.data-section::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.data-section::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Анимации */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.container {
    animation: fadeIn 0.3s ease-out;
}

/* Responsive */
@media (max-width: 380px) {
    body {
        width: 350px;
    }
    
    .controls {
        flex-direction: column;
    }
    
    .btn {
        min-width: auto;
    }
}
