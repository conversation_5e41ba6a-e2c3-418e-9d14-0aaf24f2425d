<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Icon Generator</title>
</head>
<body>
    <canvas id="icon16" width="16" height="16"></canvas>
    <canvas id="icon48" width="48" height="48"></canvas>
    <canvas id="icon128" width="128" height="128"></canvas>

    <script>
        // Функция для создания иконки
        function createIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            
            // Фон
            ctx.fillStyle = '#3498db';
            ctx.fillRect(0, 0, size, size);
            
            // Белая буква "G"
            ctx.fillStyle = 'white';
            ctx.font = `bold ${size * 0.6}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('G', size / 2, size / 2);
            
            // Скачиваем иконку
            canvas.toBlob(function(blob) {
                const link = document.createElement('a');
                link.download = `icon${size}.png`;
                link.href = URL.createObjectURL(blob);
                link.click();
            });
        }

        // Создаем все иконки
        createIcon(document.getElementById('icon16'), 16);
        createIcon(document.getElementById('icon48'), 48);
        createIcon(document.getElementById('icon128'), 128);
    </script>
</body>
</html>
