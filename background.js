// Фоновый скрипт для Chrome Extension
chrome.runtime.onInstalled.addListener(() => {
    console.log('Goszakup Parser установлен');
});

// Обработка сообщений между компонентами расширения
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'updateData') {
        // Сохраняем данные в storage
        chrome.storage.local.set({ 
            parsedData: request.data,
            lastUpdated: new Date().toISOString()
        });
        
        // Обновляем badge расширения
        const dataCount = Object.keys(request.data).length;
        chrome.action.setBadgeText({
            text: dataCount > 0 ? dataCount.toString() : ''
        });
        
        chrome.action.setBadgeBackgroundColor({
            color: '#28a745'
        });
    }
});

// Обновление badge при активации вкладки
chrome.tabs.onActivated.addListener(async (activeInfo) => {
    const tab = await chrome.tabs.get(activeInfo.tabId);
    
    if (tab.url && (tab.url.includes('v3bl.goszakup.gov.kz') || tab.url.includes('zakup.sk.kz'))) {
        // Если мы на поддерживаемом сайте, показываем зеленый badge
        chrome.action.setBadgeBackgroundColor({
            color: '#28a745'
        });
        
        // Проверяем есть ли сохраненные данные
        chrome.storage.local.get(['parsedData'], (result) => {
            if (result.parsedData) {
                const dataCount = Object.keys(result.parsedData).length;
                chrome.action.setBadgeText({
                    text: dataCount > 0 ? dataCount.toString() : ''
                });
            }
        });
    } else {
        // На другом сайте показываем серый badge или убираем
        chrome.action.setBadgeText({ text: '' });
    }
});

// Контекстное меню (правый клик)
chrome.runtime.onInstalled.addListener(() => {
    // Контекстное меню для goszakup.gov.kz
    chrome.contextMenus.create({
        id: 'parseGoszakupData',
        title: 'Парсить данные Goszakup',
        contexts: ['page'],
        documentUrlPatterns: ['https://v3bl.goszakup.gov.kz/*']
    });
    
    // Контекстное меню для zakup.sk.kz
    chrome.contextMenus.create({
        id: 'parseSamrukData',
        title: 'Парсить данные Самрук Казына',
        contexts: ['page'],
        documentUrlPatterns: ['https://zakup.sk.kz/*']
    });
});

chrome.contextMenus.onClicked.addListener((info, tab) => {
    if (info.menuItemId === 'parseGoszakupData' || info.menuItemId === 'parseSamrukData') {
        chrome.tabs.sendMessage(tab.id, { action: 'parseData' });
    }
});

// Автоматическое обновление данных при навигации
chrome.webNavigation.onCompleted.addListener((details) => {
    if (details.url.includes('v3bl.goszakup.gov.kz') && details.frameId === 0) {
        // Ждем немного, чтобы страница полностью загрузилась
        setTimeout(() => {
            chrome.tabs.sendMessage(details.tabId, { 
                action: 'checkForAutoparse' 
            }).catch(() => {
                // Игнорируем ошибки, если content script еще не загружен
            });
        }, 2000);
    }
}, {
    url: [{ hostContains: 'v3bl.goszakup.gov.kz' }]
});

// Обработка ошибок
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'logError') {
        console.error('Goszakup Parser Error:', request.error);
        
        // Можно добавить отправку ошибок в аналитику
        // или показать уведомление пользователю
        chrome.action.setBadgeText({ text: '!' });
        chrome.action.setBadgeBackgroundColor({ color: '#dc3545' });
        
        setTimeout(() => {
            chrome.action.setBadgeText({ text: '' });
        }, 5000);
    }
});

// Очистка данных при закрытии вкладки (опционально)
chrome.tabs.onRemoved.addListener((tabId, removeInfo) => {
    // Можно добавить логику очистки временных данных
    console.log('Tab closed:', tabId);
});
