{"version": "2.0.0", "tasks": [{"label": "Test Extension", "type": "shell", "command": "powershell.exe", "args": ["-Command", "Write-Host 'Extension files updated successfully. Please reload the extension in Chrome to test the improved sum parsing.' -ForegroundColor Green; Write-Host 'Changes made:' -ForegroundColor Yellow; Write-Host '1. Fixed parseAnnouncedSumFromPage() to prioritize planned/announced sum fields' -ForegroundColor Cyan; Write-Host '2. Added parseGoszakupFinancialFields() for better financial data parsing' -ForegroundColor Cyan; Write-Host '3. Now distinguishes between unit price and total announced sum' -ForegroundColor Cyan; Write-Host 'Please test on v3bl.goszakup.gov.kz pages.' -ForegroundColor Magenta"], "group": "test", "isBackground": false}]}